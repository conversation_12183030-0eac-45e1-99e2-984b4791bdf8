#!/usr/bin/env nextflow

nextflow.enable.dsl = 2

params.genome       = null  // No default value, must be provided by user
params.outdir       = "$baseDir/results"
params.gtdbtk_db    = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220"
params.hmm_base_dir = "/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept"
params.batch_count  = 66  // Default number of batches to process
params.scaler_path  = "$baseDir/robustscaler_enc1024_layers1.pkl"  // Path to the saved scaler
params.model_path   = "$baseDir/robustscaler_enc1024_layers1.h5"  // Path to the saved autoencoder model
params.autoencoder_output = "encoded_features.csv"  // Output file for autoencoder results
params.ml_model_path = "$baseDir/best_refined_model_RS.pkl"  // Path to the ML model for prediction
params.prediction_output = "prediction_results.csv"  // Output file for prediction results
params.predict_version = 1  // Version parameter for the MODEL_PREDICT process, change to force re-execution

// Print workflow header will be done in the workflow

// Prodigal
process PRODIGAL {
    publishDir "${params.outdir}/prodigal", mode: 'copy'

    input:
    path genome
    path script

    output:
    path "${genome.baseName}.faa", emit: faa

    script:
    """
    python ${script} ${genome}
    """
}

// GTDB-Tk
process GTDBTK {
    publishDir "${params.outdir}/gtdbtk", mode: 'copy'

    input:
    path genome
    path script

    output:
    path "gtdb_classification.csv", emit: classification

    script:
    """
    mkdir -p genome_dir
    cp ${genome} genome_dir/
    python ${script} --genome_dir genome_dir --data_path ${params.gtdbtk_db} --clean_outputs --csv_output gtdb_classification.csv
    """
}



// HMMSEARCH for any batch
process HMMSEARCH {
    tag "batch_${batch_id}"
    publishDir "${params.outdir}/hmmsearch/batch_${batch_id}", mode: 'copy'

    input:
    each path(faa)
    each path(script)
    each batch_id

    output:
    tuple val(batch_id), path("batch_${batch_id}"), emit: hits_dir

    script:
    """
    mkdir -p batch_${batch_id}
    python ${script} ${params.hmm_base_dir}/batch_${batch_id} ${faa} batch_${batch_id} --num_processes ${task.cpus}
    """
}

// Process hits for any batch
process PROCESS_HITS {
    tag "batch_${batch_id}"
    publishDir "${params.outdir}/processed_hits/batch_${batch_id}", mode: 'copy', saveAs: { filename -> filename.endsWith(".csv") ? filename : null }

    input:
    tuple val(batch_id), path(hits_dir)
    each path(script)

    output:
    tuple val(batch_id), path("*.csv"), emit: csv_hits

    script:
    """
    echo "Running convert_hits.py for batch batch_${batch_id}..."
    python ${script} ${hits_dir} .
    """
}

// Merge all processed results
process CREATE_DATAFRAME {
    publishDir "${params.outdir}", mode: 'copy'

    input:
    path csv_files
    path classification
    path script
    val batch_count

    output:
    path "final_results.csv", emit: final_results

    // Skip this process if the output file already exists
    when:
    !file("${params.outdir}/final_results.csv").exists()

    script:
    """
    # Create directory structure expected by the script
    mkdir -p csv_hits
    for i in \$(seq 1 ${batch_count}); do
        mkdir -p csv_hits/batch_\$i
    done

    # Debug: List all CSV files
    echo "All CSV files to process:"
    ls -la ${csv_files} || echo "No CSV files found"

    # Get the project root directory (where main.nf is located)
    PROJECT_ROOT=\$(dirname \$(readlink -f main.nf))
    echo "Project root: \$PROJECT_ROOT"

    # Get the output directory path
    OUTDIR="\$PROJECT_ROOT/${params.outdir.tokenize('/')[-1]}"
    echo "Output directory: \$OUTDIR"

    # Copy files from the output directory's processed_hits directly
    echo "Copying files from \$OUTDIR/processed_hits"

    # Check if the directory exists
    if [ ! -d "\$OUTDIR/processed_hits" ]; then
        echo "Directory \$OUTDIR/processed_hits does not exist"
        echo "Current directory: \$(pwd)"
        echo "Listing current directory:"
        ls -la
        echo "Listing project root:"
        ls -la \$PROJECT_ROOT
        exit 1
    fi

    # Find all batch directories in the processed_hits directory
    BATCH_DIRS=\$(find \$OUTDIR/processed_hits -maxdepth 1 -type d -name "batch_*" | sort)

    if [ -z "\$BATCH_DIRS" ]; then
        echo "No batch directories found in \$OUTDIR/processed_hits"
        exit 1
    fi

    # Process each batch directory
    for BATCH_DIR in \$BATCH_DIRS; do
        BATCH_NAME=\$(basename \$BATCH_DIR)
        echo "Processing \$BATCH_NAME"

        # Extract batch number
        BATCH_NUM=\$(echo \$BATCH_NAME | sed 's/batch_//')

        # Create the corresponding directory in csv_hits if it doesn't exist
        mkdir -p csv_hits/\$BATCH_NAME

        # Copy CSV files using absolute paths
        find "\$OUTDIR/processed_hits/\$BATCH_NAME" -name "*.csv" -exec cp {} csv_hits/\$BATCH_NAME/ \\;
    done

    # Debug: List the contents of the csv_hits directory
    echo "Contents of csv_hits directory:"
    find csv_hits -type f | sort

    # Run the script with the organized directory structure
    python ${script} csv_hits ${classification} final_results.csv
    """
}

// Process data with autoencoder
process AUTOENCODER {
    publishDir "${params.outdir}", mode: 'copy'

    input:
    path input_csv
    path scaler
    path model
    path script

    output:
    path "${params.autoencoder_output}", emit: encoded_features

    // Skip this process if the output file already exists
    when:
    !file("${params.outdir}/${params.autoencoder_output}").exists()

    script:
    """
    # Run the autoencoder tool to encode the data
    python ${script} encode ${input_csv} ${scaler} ${model} ${params.autoencoder_output}
    """
}

// Make predictions using the ML model
process MODEL_PREDICT {
    publishDir "${params.outdir}", mode: 'copy'

    // Add a version parameter that can be changed to force re-execution
    input:
    path encoded_features
    path ml_model
    path script
    val version

    output:
    path "${params.prediction_output}", emit: prediction_results

    script:
    """
    echo "Running model prediction version: ${version}"
    # Run the model prediction script
    python ${script} --predict-only --model-path ${ml_model} --sample-path ${encoded_features} --output-path ${params.prediction_output}
    """
}

workflow {
    // Print workflow header
    log.info """
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: ${params.genome}
 outdir     : ${params.outdir}
 gtdbtk_db  : ${params.gtdbtk_db}
 hmm_base_dir: ${params.hmm_base_dir}
 processing batches: 1 to ${params.batch_count}
 scaler path: ${params.scaler_path}
 model path : ${params.model_path}
 autoencoder output: ${params.autoencoder_output}
 ML model path: ${params.ml_model_path}
 prediction output: ${params.prediction_output}
 prediction version: ${params.predict_version}
==============================================
"""

    // Validate parameters
    if (params.genome == null) exit 1, "Genome parameter is required: please provide --genome"
    if (!file(params.genome).exists()) exit 1, "Missing genome file: ${params.genome}"
    if (!file(params.gtdbtk_db).exists()) exit 1, "Missing GTDB-Tk DB: ${params.gtdbtk_db}"
    if (!file(params.hmm_base_dir).exists()) exit 1, "Missing HMM base dir: ${params.hmm_base_dir}"
    if (!file(params.scaler_path).exists()) exit 1, "Missing scaler file: ${params.scaler_path}"
    if (!file(params.model_path).exists()) exit 1, "Missing model file: ${params.model_path}"
    if (!file(params.ml_model_path).exists()) exit 1, "Missing ML model file: ${params.ml_model_path}"

    // Check if batch directories exist
    (1..params.batch_count).each { i ->
        def batch_dir = file("${params.hmm_base_dir}/batch_${i}")
        if (!batch_dir.exists()) exit 1, "Batch directory not found: ${batch_dir}"
    }

    // Create output directory
    file(params.outdir).mkdirs()

    // Define channels
    genome_ch = Channel.fromPath(params.genome)
    scaler_ch = Channel.fromPath(params.scaler_path)
    model_ch = Channel.fromPath(params.model_path)
    ml_model_ch = Channel.fromPath(params.ml_model_path)

    // Create a channel for batch IDs
    batch_ids_ch = Channel.of(1..params.batch_count)

    // Script channels
    prodigal_script_ch = Channel.fromPath("bin/run_prodigal_and_rename.py")
    gtdbtk_script_ch = Channel.fromPath("bin/run_gtdbtk.py")
    hmmsearch_script_ch = Channel.fromPath("bin/hmmsearch_python.py")
    convert_hits_script_ch = Channel.fromPath("bin/convert_hits.py")
    create_df_script_ch = Channel.fromPath("bin/create_unique_df_hits_optimized.py")
    autoencoder_script_ch = Channel.fromPath("autoencoder_tool.py")
    model_predict_script_ch = Channel.fromPath("${baseDir}/model_load_and_predict.py")

    // Step 1: Run Prodigal
    prodigal_result = PRODIGAL(genome_ch, prodigal_script_ch)

    // Step 2: Run GTDB-Tk
    gtdbtk_result = GTDBTK(genome_ch, gtdbtk_script_ch)

    // Step 3: Run HMMSEARCH for each batch
    hmmsearch_results = HMMSEARCH(prodigal_result.faa, hmmsearch_script_ch, batch_ids_ch)

    // Step 4: Process HMMSEARCH hits for each batch
    // We need to run this step but don't need to use its output directly
    PROCESS_HITS(hmmsearch_results.hits_dir, convert_hits_script_ch)

    // Step 5: Create final dataframe with all results or use existing one
    // Check if final_results.csv already exists
    final_results_file = file("${params.outdir}/final_results.csv")

    if (final_results_file.exists()) {
        log.info "Found existing final_results.csv, skipping CREATE_DATAFRAME process"
        final_results_ch = Channel.fromPath(final_results_file)
        final_results = [final_results: final_results_ch]
    } else {
        // We'll just pass a dummy file to CREATE_DATAFRAME since it will find the files directly
        dummy_file = file("${baseDir}/main.nf")

        final_results = CREATE_DATAFRAME(
            dummy_file,
            gtdbtk_result.classification,
            create_df_script_ch,
            params.batch_count
        )
    }

    // Step 6: Process the final results with the autoencoder or use existing encoded features
    encoded_features_file = file("${params.outdir}/${params.autoencoder_output}")

    if (encoded_features_file.exists()) {
        log.info "Found existing ${params.autoencoder_output}, skipping AUTOENCODER process"
        encoded_features_ch = Channel.fromPath(encoded_features_file)
        autoencoder_results = [encoded_features: encoded_features_ch]
    } else {
        autoencoder_results = AUTOENCODER(
            final_results.final_results,
            scaler_ch,
            model_ch,
            autoencoder_script_ch
        )
    }

    // Step 7: Make predictions using the ML model
    MODEL_PREDICT(
        autoencoder_results.encoded_features,
        ml_model_ch,
        model_predict_script_ch,
        params.predict_version
    )
}
