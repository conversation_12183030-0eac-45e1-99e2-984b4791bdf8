May-27 10:08:50.188 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax -resume
May-27 10:08:50.275 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 10:08:50.296 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 10:08:50.320 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 10:08:50.321 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 10:08:50.325 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 10:08:50.337 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 10:08:50.355 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:08:50.358 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:08:50.396 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 10:08:50.399 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@23cd4ff2] - activable => nextflow.secret.LocalSecretsProvider@23cd4ff2
May-27 10:08:50.411 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 10:08:50.919 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 10:08:50.935 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [infallible_yonath] DSL2 - revision: 6ec682ce41
May-27 10:08:50.936 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 10:08:50.936 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 10:08:50.985 [main] DEBUG nextflow.Session - Session UUID: ae39cc0b-04fe-4119-b166-3b8bb9b6b64c
May-27 10:08:50.985 [main] DEBUG nextflow.Session - Run name: infallible_yonath
May-27 10:08:50.987 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 10:08:50.995 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 10:08:51.000 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:08:51.022 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (20.2 GB) - Swap: 8 GB (0)
May-27 10:08:51.059 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 10:08:51.098 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 10:08:51.106 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 10:08:51.111 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 10:08:51.134 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 10:08:51.142 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 10:08:51.292 [main] DEBUG nextflow.Session - Session start
May-27 10:08:51.978 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 10:08:52.016 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 4
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-27 10:08:52.106 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 10:08:52.117 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.117 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.122 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 10:08:52.127 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 10:08:52.129 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 10:08:52.149 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 10:08:52.190 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.190 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.193 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MOCK_CLASSIFICATION': maxForks=0; fair=false; array=0
May-27 10:08:52.210 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 10:08:52.229 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.229 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.230 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 10:08:52.232 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 10:08:52.245 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 10:08:52.247 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.248 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.250 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 10:08:52.252 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 10:08:52.274 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-27 10:08:52.276 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.278 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.282 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-27 10:08:52.299 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.300 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.314 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 10:08:52.332 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:08:52.344 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:08:52.356 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 10:08:52.363 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, MOCK_CLASSIFICATION, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 10:08:52.366 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
May-27 10:08:52.384 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 10:08:52.384 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MOCK_CLASSIFICATION
May-27 10:08:52.393 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:08:52.399 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:08:52.403 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:08:52.406 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:08:52.410 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-27 10:08:52.413 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 10:08:52.416 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 10:08:52.420 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f522baca807c5408: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 10:08:52.421 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 10:08:52.424 [main] DEBUG nextflow.Session - Session await
May-27 10:08:52.517 [Actor Thread 15] INFO  nextflow.processor.TaskProcessor - [8b/af0c72] Cached process > PRODIGAL (1)
May-27 10:08:52.517 [Actor Thread 19] INFO  nextflow.processor.TaskProcessor - [a0/6f7319] Cached process > MOCK_CLASSIFICATION (1)
May-27 10:08:52.542 [Actor Thread 19] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:08:52.576 [Actor Thread 15] INFO  nextflow.processor.TaskProcessor - [17/a04c06] Cached process > HMMSEARCH (batch_1)
May-27 10:08:52.576 [Actor Thread 19] INFO  nextflow.processor.TaskProcessor - [e6/374f24] Cached process > HMMSEARCH (batch_2)
May-27 10:08:52.578 [Actor Thread 17] INFO  nextflow.processor.TaskProcessor - [ed/9de503] Cached process > HMMSEARCH (batch_4)
May-27 10:08:52.578 [Actor Thread 32] INFO  nextflow.processor.TaskProcessor - [f3/14ddf9] Cached process > HMMSEARCH (batch_3)
May-27 10:08:53.559 [Actor Thread 31] INFO  nextflow.processor.TaskProcessor - [b4/61d1f9] Cached process > PROCESS_HITS (batch_2)
May-27 10:08:53.575 [Actor Thread 29] INFO  nextflow.processor.TaskProcessor - [00/a0da25] Cached process > PROCESS_HITS (batch_1)
May-27 10:08:53.589 [Actor Thread 30] INFO  nextflow.processor.TaskProcessor - [fd/fef727] Cached process > PROCESS_HITS (batch_3)
May-27 10:08:53.597 [Actor Thread 27] INFO  nextflow.processor.TaskProcessor - [1f/5bfb56] Cached process > PROCESS_HITS (batch_4)
May-27 10:08:54.031 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:08:54.034 [Task submitter] INFO  nextflow.Session - [fd/af015f] Submitted process > CREATE_DATAFRAME (1)
May-27 10:08:58.648 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/af015f1ba13b9db66feae5182ff1c4]
May-27 10:08:58.649 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:08:58.684 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:08:58.684 [Task submitter] INFO  nextflow.Session - [47/14f8f4] Submitted process > AUTOENCODER (1)
May-27 10:09:21.169 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: AUTOENCODER (1); status: COMPLETED; exit: 1; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/14f8f4fb5f9152d8b6012aae9e28f7]
May-27 10:09:21.176 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=AUTOENCODER (1); work-dir=/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/14f8f4fb5f9152d8b6012aae9e28f7
  error [nextflow.exception.ProcessFailedException]: Process `AUTOENCODER (1)` terminated with an error exit status (1)
May-27 10:09:21.194 [TaskFinalizer-2] ERROR nextflow.processor.TaskProcessor - Error executing process > 'AUTOENCODER (1)'

Caused by:
  Process `AUTOENCODER (1)` terminated with an error exit status (1)


Command executed:

  # Run the autoencoder tool to encode the data
  python autoencoder_tool.py encode final_results.csv robustscaler_enc1024_layers1.pkl robustscaler_enc1024_layers1.h5 encoded_features.csv

Command exit status:
  1

Command output:
  Encoding data from final_results.csv using the saved autoencoder model...
  
  === Checking Compatibility ===
  Checking compatibility of final_results.csv with the saved model...
  The model expects 97614 input features.
  Input file has 222 total columns.
  Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
  Found 214 potential feature columns.
  After excluding Orthogroup138309: 214 feature columns.
  ❌ The number of features (214) does NOT match what the model expects (97614).
     Missing 97400 features.
  Compatibility check failed. Please fix the issues before encoding.

Command error:
  2025-05-27 10:09:04.240004: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
  WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
  E0000 00:00:1748365744.547392  202863 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
  E0000 00:00:1748365744.644561  202863 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
  W0000 00:00:1748365745.607823  202863 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365745.607852  202863 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365745.607856  202863 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365745.607858  202863 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  2025-05-27 10:09:05.680338: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
  To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
  Encoding data from final_results.csv using the saved autoencoder model...
  
  === Checking Compatibility ===
  Checking compatibility of final_results.csv with the saved model...
  The model expects 97614 input features.
  Input file has 222 total columns.
  Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
  Found 214 potential feature columns.
  After excluding Orthogroup138309: 214 feature columns.
  ❌ The number of features (214) does NOT match what the model expects (97614).
     Missing 97400 features.
  Compatibility check failed. Please fix the issues before encoding.

Work dir:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/47/14f8f4fb5f9152d8b6012aae9e28f7

Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
May-27 10:09:21.198 [TaskFinalizer-2] DEBUG nextflow.Session - Session aborted -- Cause: Process `AUTOENCODER (1)` terminated with an error exit status (1)
May-27 10:09:21.199 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 10:09:21.218 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 10:09:21.218 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 10:09:21.226 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=1; failedCount=1; ignoredCount=0; cachedCount=10; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=18s; failedDuration=22.4s; cachedDuration=12m 55s;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=4; peakMemory=8 GB; ]
May-27 10:09:21.436 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 10:09:21.453 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
