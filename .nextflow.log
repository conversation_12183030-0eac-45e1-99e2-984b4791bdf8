May-27 10:35:14.178 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_025823245.1.fna --skip_tax
May-27 10:35:14.316 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 10:35:14.336 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 10:35:14.362 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 10:35:14.363 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 10:35:14.367 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 10:35:14.381 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 10:35:14.400 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:35:14.402 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:35:14.437 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 10:35:14.441 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
May-27 10:35:14.452 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 10:35:15.145 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 10:35:15.159 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [jovial_heisenberg] DSL2 - revision: 3fa433d128
May-27 10:35:15.161 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 10:35:15.162 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 10:35:15.207 [main] DEBUG nextflow.Session - Session UUID: 27196a89-6637-407a-98f1-f1a6009cbbd1
May-27 10:35:15.208 [main] DEBUG nextflow.Session - Run name: jovial_heisenberg
May-27 10:35:15.224 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 10:35:15.257 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 10:35:15.268 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:35:15.327 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (23.4 GB) - Swap: 8 GB (0)
May-27 10:35:15.389 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 10:35:15.430 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 10:35:15.438 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 10:35:15.442 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 10:35:15.465 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 10:35:15.474 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 10:35:15.615 [main] DEBUG nextflow.Session - Session start
May-27 10:35:16.305 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 10:35:16.343 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_025823245.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 4
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-27 10:35:16.440 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 10:35:16.450 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.451 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.458 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 10:35:16.463 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 10:35:16.465 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 10:35:16.485 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 10:35:16.540 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 10:35:16.559 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.559 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.560 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 10:35:16.562 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 10:35:16.575 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 10:35:16.577 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.578 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.580 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 10:35:16.581 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 10:35:16.592 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-27 10:35:16.593 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.594 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.596 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-27 10:35:16.609 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.609 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.610 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 10:35:16.618 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:35:16.619 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:35:16.620 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 10:35:16.624 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 10:35:16.627 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-27 10:35:16.642 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 10:35:16.642 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:35:16.644 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:35:16.645 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:35:16.647 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:35:16.648 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-27 10:35:16.651 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 10:35:16.653 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 10:35:16.655 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_7497f5345efb16f3: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 10:35:16.660 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 10:35:16.661 [main] DEBUG nextflow.Session - Session await
May-27 10:35:16.693 [Actor Thread 21] DEBUG nextflow.processor.TaskProcessor - Process CREATE_DATAFRAME > collision check staging file names: [create_unique_df_hits_optimized.py:1, main.nf:2]
May-27 10:35:16.696 [Actor Thread 21] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `CREATE_DATAFRAME` input file name collision -- There are multiple input files for each of the following file names: main.nf
May-27 10:35:16.712 [Actor Thread 21] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Process `CREATE_DATAFRAME` input file name collision -- There are multiple input files for each of the following file names: main.nf



Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
May-27 10:35:16.714 [Actor Thread 21] DEBUG nextflow.Session - Session aborted -- Cause: Process `CREATE_DATAFRAME` input file name collision -- There are multiple input files for each of the following file names: main.nf
May-27 10:35:16.730 [Actor Thread 21] DEBUG nextflow.Session - The following nodes are still active:
[process] PRODIGAL
  status=ACTIVE
  port 0: (queue) closed; channel: genome
  port 1: (queue) OPEN  ; channel: script
  port 2: (cntrl) -     ; channel: $

[process] HMMSEARCH
  status=ACTIVE
  port 0: (value) OPEN  ; channel: __$eachinparam<0>
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (value) bound ; channel: __$eachinparam<2>
  port 3: (cntrl) -     ; channel: $

[process] PROCESS_HITS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (cntrl) -     ; channel: $

[process] CREATE_DATAFRAME
  status=ACTIVE
  port 0: (value) bound ; channel: csv_files
  port 1: (queue) closed; channel: classification
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: batch_count
  port 4: (cntrl) -     ; channel: $

[process] AUTOENCODER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: input_csv
  port 1: (queue) OPEN  ; channel: scaler
  port 2: (queue) OPEN  ; channel: model
  port 3: (queue) OPEN  ; channel: script
  port 4: (cntrl) -     ; channel: $

[process] MODEL_PREDICT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: encoded_features
  port 1: (queue) OPEN  ; channel: ml_model
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: version
  port 4: (cntrl) -     ; channel: $

May-27 10:35:16.734 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 10:35:16.734 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 10:35:16.735 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 10:35:16.736 [Actor Thread 8] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=PRODIGAL; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
May-27 10:35:16.736 [Actor Thread 18] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
May-27 10:35:16.742 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=0ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=0; peakCpus=0; peakMemory=0; ]
May-27 10:35:16.934 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 10:35:16.970 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
