May-27 10:05:40.197 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-27 10:05:40.274 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 10:05:40.294 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 10:05:40.333 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 10:05:40.335 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 10:05:40.339 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 10:05:40.349 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 10:05:40.370 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:05:40.372 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:05:40.396 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 10:05:40.398 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@1e6b9a95] - activable => nextflow.secret.LocalSecretsProvider@1e6b9a95
May-27 10:05:40.422 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 10:05:40.926 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 10:05:40.941 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [insane_bardeen] DSL2 - revision: 14b9d5d5d8
May-27 10:05:40.943 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 10:05:40.943 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 10:05:40.987 [main] DEBUG nextflow.Session - Session UUID: ae39cc0b-04fe-4119-b166-3b8bb9b6b64c
May-27 10:05:40.988 [main] DEBUG nextflow.Session - Run name: insane_bardeen
May-27 10:05:40.989 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 10:05:40.997 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 10:05:41.002 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:05:41.036 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (22.7 GB) - Swap: 8 GB (0)
May-27 10:05:41.084 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 10:05:41.118 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 10:05:41.127 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 10:05:41.131 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 10:05:41.158 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 10:05:41.166 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 10:05:41.291 [main] DEBUG nextflow.Session - Session start
May-27 10:05:41.947 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 10:05:41.985 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 4
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-27 10:05:42.086 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 10:05:42.097 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.098 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.104 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 10:05:42.109 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 10:05:42.111 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 10:05:42.132 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 10:05:42.172 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.172 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.175 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MOCK_CLASSIFICATION': maxForks=0; fair=false; array=0
May-27 10:05:42.193 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 10:05:42.210 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.211 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.216 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 10:05:42.219 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 10:05:42.230 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 10:05:42.233 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.236 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.239 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 10:05:42.240 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 10:05:42.257 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-27 10:05:42.259 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.260 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.262 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-27 10:05:42.272 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.272 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.274 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 10:05:42.281 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:05:42.281 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:05:42.282 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 10:05:42.285 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, MOCK_CLASSIFICATION, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 10:05:42.288 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
May-27 10:05:42.294 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 10:05:42.294 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MOCK_CLASSIFICATION
May-27 10:05:42.296 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:05:42.297 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:05:42.303 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:05:42.303 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:05:42.304 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-27 10:05:42.309 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 10:05:42.312 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 10:05:42.316 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_d473b4edd20e4054: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 10:05:42.317 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 10:05:42.318 [main] DEBUG nextflow.Session - Session await
May-27 10:05:42.449 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:42.451 [Task submitter] INFO  nextflow.Session - [8b/af0c72] Submitted process > PRODIGAL (1)
May-27 10:05:42.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:42.469 [Task submitter] INFO  nextflow.Session - [a0/6f7319] Submitted process > MOCK_CLASSIFICATION (1)
May-27 10:05:42.526 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: MOCK_CLASSIFICATION (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a0/6f7319fcee69f436d35675c2e5daaf]
May-27 10:05:42.526 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:05:42.550 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:05:49.925 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8b/af0c72cc85719f1927e8d03d623b5b]
May-27 10:05:49.991 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:49.991 [Task submitter] INFO  nextflow.Session - [17/a04c06] Submitted process > HMMSEARCH (batch_1)
May-27 10:05:50.031 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:50.032 [Task submitter] INFO  nextflow.Session - [f3/14ddf9] Submitted process > HMMSEARCH (batch_3)
May-27 10:05:59.107 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f3/14ddf9f71f38d5e5de22a7fa3b1bae]
May-27 10:05:59.138 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:59.139 [Task submitter] INFO  nextflow.Session - [ed/9de503] Submitted process > HMMSEARCH (batch_4)
May-27 10:05:59.488 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/17/a04c06d4d2f5c8df205e093e5a8f8a]
May-27 10:05:59.510 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:05:59.511 [Task submitter] INFO  nextflow.Session - [e6/374f24] Submitted process > HMMSEARCH (batch_2)
May-27 10:06:09.065 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/9de503a8b7b1bb160316394cb0c312]
May-27 10:06:09.096 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:06:09.096 [Task submitter] INFO  nextflow.Session - [fd/fef727] Submitted process > PROCESS_HITS (batch_3)
May-27 10:06:09.121 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:06:09.122 [Task submitter] INFO  nextflow.Session - [00/a0da25] Submitted process > PROCESS_HITS (batch_1)
May-27 10:06:09.135 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:06:09.136 [Task submitter] INFO  nextflow.Session - [1f/5bfb56] Submitted process > PROCESS_HITS (batch_4)
May-27 10:06:09.834 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e6/374f2444d08b5681ddb7c30b369dc7]
May-27 10:06:09.901 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:06:09.902 [Task submitter] INFO  nextflow.Session - [b4/61d1f9] Submitted process > PROCESS_HITS (batch_2)
May-27 10:06:27.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/fef727e3549d03b2a0adff527349b1]
May-27 10:06:27.924 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/00/a0da25879fae6c1c5a7e90fe900707]
May-27 10:06:28.344 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/1f/5bfb563390241329dd3bd87b008ec2]
May-27 10:06:28.664 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b4/61d1f96336bfd542060a6e344d272b]
May-27 10:06:28.872 [Actor Thread 29] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Not a valid path value type: java.lang.Integer (3)
May-27 10:06:28.881 [Actor Thread 29] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Not a valid path value type: java.lang.Integer (3)



Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
May-27 10:06:28.883 [Actor Thread 29] DEBUG nextflow.Session - Session aborted -- Cause: Not a valid path value type: java.lang.Integer (3)
May-27 10:06:28.894 [Actor Thread 29] DEBUG nextflow.Session - The following nodes are still active:
[process] CREATE_DATAFRAME
  status=ACTIVE
  port 0: (value) bound ; channel: csv_files
  port 1: (queue) closed; channel: classification
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: batch_count
  port 4: (cntrl) -     ; channel: $

[process] AUTOENCODER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: input_csv
  port 1: (queue) OPEN  ; channel: scaler
  port 2: (queue) OPEN  ; channel: model
  port 3: (queue) OPEN  ; channel: script
  port 4: (cntrl) -     ; channel: $

[process] MODEL_PREDICT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: encoded_features
  port 1: (queue) OPEN  ; channel: ml_model
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: version
  port 4: (cntrl) -     ; channel: $

May-27 10:06:28.897 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 10:06:28.897 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 10:06:28.897 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 10:06:28.900 [Actor Thread 33] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
May-27 10:06:28.903 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=10; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=12m 55s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=4; peakCpus=32; peakMemory=20 GB; ]
May-27 10:06:29.104 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 10:06:29.141 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
