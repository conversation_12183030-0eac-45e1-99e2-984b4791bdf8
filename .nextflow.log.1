May-27 10:17:20.635 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax --batch_count 2
May-27 10:17:20.743 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 10:17:20.764 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 10:17:20.785 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 10:17:20.788 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 10:17:20.790 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 10:17:20.803 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 10:17:20.821 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:17:20.824 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:17:20.849 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 10:17:20.852 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@400d912a] - activable => nextflow.secret.LocalSecretsProvider@400d912a
May-27 10:17:20.876 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 10:17:21.367 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 10:17:21.384 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [loving_escher] DSL2 - revision: **********
May-27 10:17:21.386 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 10:17:21.386 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 10:17:21.431 [main] DEBUG nextflow.Session - Session UUID: 0026c8e2-0d03-4863-81fe-7c902de3040b
May-27 10:17:21.432 [main] DEBUG nextflow.Session - Run name: loving_escher
May-27 10:17:21.443 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 10:17:21.455 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 10:17:21.460 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:17:21.480 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (4.3 GB) - Swap: 8 GB (8 KB)
May-27 10:17:21.548 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 10:17:21.583 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 10:17:21.596 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 10:17:21.601 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 10:17:21.638 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 10:17:21.646 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 10:17:21.778 [main] DEBUG nextflow.Session - Session start
May-27 10:17:22.506 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 10:17:22.546 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 2
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-27 10:17:22.650 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 10:17:22.668 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.668 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.675 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 10:17:22.681 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 10:17:22.683 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 10:17:22.703 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 10:17:22.757 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 10:17:22.776 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.776 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.783 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 10:17:22.807 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 10:17:22.826 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 10:17:22.829 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.834 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.837 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 10:17:22.842 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 10:17:22.865 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-27 10:17:22.867 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.869 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.871 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-27 10:17:22.880 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.881 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.884 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 10:17:22.892 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:17:22.897 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:17:22.935 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 10:17:22.988 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 10:17:23.014 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-27 10:17:23.031 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 10:17:23.045 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:17:23.062 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:17:23.068 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:17:23.074 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:17:23.081 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-27 10:17:23.125 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 10:17:23.134 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 10:17:23.135 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_32120efd6efe8406: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 10:17:23.141 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 10:17:23.144 [main] DEBUG nextflow.Session - Session await
May-27 10:17:23.195 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:17:23.197 [Task submitter] INFO  nextflow.Session - [88/5ce77c] Submitted process > PRODIGAL (1)
May-27 10:17:30.438 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/5ce77c0befb9b1cce84c4ec20f587a]
May-27 10:17:30.439 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:17:30.462 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:17:30.507 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:17:30.507 [Task submitter] INFO  nextflow.Session - [b3/68e6a0] Submitted process > HMMSEARCH (batch_2)
May-27 10:17:30.568 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:17:30.568 [Task submitter] INFO  nextflow.Session - [7e/559afd] Submitted process > HMMSEARCH (batch_1)
May-27 10:17:39.873 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7e/559afdd60a6769d758ba0a90c538d9]
May-27 10:17:39.928 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:17:39.929 [Task submitter] INFO  nextflow.Session - [6e/db29ca] Submitted process > PROCESS_HITS (batch_1)
May-27 10:17:40.016 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b3/68e6a0513f5332d45562728c9606d0]
May-27 10:17:40.094 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:17:40.095 [Task submitter] INFO  nextflow.Session - [bb/94fab2] Submitted process > PROCESS_HITS (batch_2)
May-27 10:17:46.265 [SIGINT handler] DEBUG nextflow.Session - Session aborted -- Cause: SIGINT
May-27 10:17:46.276 [SIGINT handler] DEBUG nextflow.Session - The following nodes are still active:
[process] CREATE_DATAFRAME
  status=ACTIVE
  port 0: (value) OPEN  ; channel: csv_files
  port 1: (queue) OPEN  ; channel: classification
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: batch_count
  port 4: (cntrl) -     ; channel: $

[process] AUTOENCODER
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: input_csv
  port 1: (queue) OPEN  ; channel: scaler
  port 2: (queue) OPEN  ; channel: model
  port 3: (queue) OPEN  ; channel: script
  port 4: (cntrl) -     ; channel: $

[process] MODEL_PREDICT
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: encoded_features
  port 1: (queue) OPEN  ; channel: ml_model
  port 2: (queue) OPEN  ; channel: script
  port 3: (value) bound ; channel: version
  port 4: (cntrl) -     ; channel: $

May-27 10:17:46.279 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 10:17:46.280 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 10:17:46.280 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 10:17:46.288 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (2)
May-27 10:17:46.294 [main] DEBUG n.executor.local.LocalTaskHandler - Unable to kill PROCESS_HITS (batch_1) -- command: kill -TERM 219589; exit: 1 
 bash: line 0: kill: (219589) - No such process

May-27 10:17:46.298 [main] DEBUG n.executor.local.LocalTaskHandler - Unable to kill PROCESS_HITS (batch_2) -- command: kill -TERM 219665; exit: 1 
 bash: line 0: kill: (219665) - No such process

May-27 10:17:46.300 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=3; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=2; succeedDuration=5m 7s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=2; peakCpus=32; peakMemory=16 GB; ]
May-27 10:17:46.493 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 10:17:46.519 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
