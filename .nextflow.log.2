May-27 10:11:41.598 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-27 10:11:41.694 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 10:11:41.713 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 10:11:41.747 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 10:11:41.749 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 10:11:41.753 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 10:11:41.765 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 10:11:41.784 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:11:41.786 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 10:11:41.811 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 10:11:41.814 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7e97551f] - activable => nextflow.secret.LocalSecretsProvider@7e97551f
May-27 10:11:41.838 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 10:11:42.354 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 10:11:42.370 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [sleepy_mayer] DSL2 - revision: 6ec682ce41
May-27 10:11:42.372 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 10:11:42.372 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 10:11:42.418 [main] DEBUG nextflow.Session - Session UUID: f8c753fc-28dd-4862-bc66-0d55ab9e4eb3
May-27 10:11:42.419 [main] DEBUG nextflow.Session - Run name: sleepy_mayer
May-27 10:11:42.420 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 10:11:42.432 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 10:11:42.436 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:11:42.465 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (13 GB) - Swap: 8 GB (4 KB)
May-27 10:11:42.501 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 10:11:42.536 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 10:11:42.549 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 10:11:42.554 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 10:11:42.577 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 10:11:42.586 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 10:11:42.757 [main] DEBUG nextflow.Session - Session start
May-27 10:11:43.441 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 10:11:43.479 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 4
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-27 10:11:43.575 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 10:11:43.585 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.586 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.592 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 10:11:43.599 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 10:11:43.601 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 10:11:43.631 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 10:11:43.677 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.677 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.678 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MOCK_CLASSIFICATION': maxForks=0; fair=false; array=0
May-27 10:11:43.696 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 10:11:43.724 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.724 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.727 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 10:11:43.728 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 10:11:43.741 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 10:11:43.743 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.743 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.747 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 10:11:43.748 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 10:11:43.753 [main] INFO  nextflow.Nextflow - Found existing final_results.csv, skipping CREATE_DATAFRAME process
May-27 10:11:43.760 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.760 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.762 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 10:11:43.770 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 10:11:43.770 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 10:11:43.772 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 10:11:43.777 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, MOCK_CLASSIFICATION, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 10:11:43.785 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
May-27 10:11:43.791 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 10:11:43.792 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MOCK_CLASSIFICATION
May-27 10:11:43.800 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:11:43.807 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 10:11:43.819 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:11:43.837 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 10:11:43.848 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 10:11:43.859 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 10:11:43.868 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f522baca807c5408: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 10:11:43.869 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 10:11:43.872 [main] DEBUG nextflow.Session - Session await
May-27 10:11:43.959 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:11:43.961 [Task submitter] INFO  nextflow.Session - [7d/e566f3] Submitted process > PRODIGAL (1)
May-27 10:11:43.986 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:11:43.987 [Task submitter] INFO  nextflow.Session - [d6/d59ed8] Submitted process > AUTOENCODER (1)
May-27 10:11:44.007 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 10:11:44.008 [Task submitter] INFO  nextflow.Session - [f2/c74885] Submitted process > MOCK_CLASSIFICATION (1)
May-27 10:11:44.070 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: MOCK_CLASSIFICATION (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f2/c74885887daac38a38f155153bfba2]
May-27 10:11:44.071 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:11:44.091 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 10:11:51.350 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: AUTOENCODER (1); status: COMPLETED; exit: 1; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/d59ed8554bfdcf940f77f5b3e19bd3]
May-27 10:11:51.356 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=AUTOENCODER (1); work-dir=/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/d59ed8554bfdcf940f77f5b3e19bd3
  error [nextflow.exception.ProcessFailedException]: Process `AUTOENCODER (1)` terminated with an error exit status (1)
May-27 10:11:51.373 [TaskFinalizer-2] ERROR nextflow.processor.TaskProcessor - Error executing process > 'AUTOENCODER (1)'

Caused by:
  Process `AUTOENCODER (1)` terminated with an error exit status (1)


Command executed:

  # Run the autoencoder tool to encode the data
  python autoencoder_tool.py encode final_results.csv robustscaler_enc1024_layers1.pkl robustscaler_enc1024_layers1.h5 encoded_features.csv

Command exit status:
  1

Command output:
  Encoding data from final_results.csv using the saved autoencoder model...
  
  === Checking Compatibility ===
  Checking compatibility of final_results.csv with the saved model...
  The model expects 97614 input features.
  Input file has 222 total columns.
  Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
  Found 214 potential feature columns.
  After excluding Orthogroup138309: 214 feature columns.
  ❌ The number of features (214) does NOT match what the model expects (97614).
     Missing 97400 features.
  Compatibility check failed. Please fix the issues before encoding.

Command error:
  2025-05-27 10:11:45.086128: E external/local_xla/xla/stream_executor/cuda/cuda_fft.cc:467] Unable to register cuFFT factory: Attempting to register factory for plugin cuFFT when one has already been registered
  WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
  E0000 00:00:1748365905.104089  204714 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
  E0000 00:00:1748365905.109660  204714 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
  W0000 00:00:1748365905.124100  204714 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365905.124116  204714 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365905.124118  204714 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  W0000 00:00:1748365905.124119  204714 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
  2025-05-27 10:11:45.128167: I tensorflow/core/platform/cpu_feature_guard.cc:210] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.
  To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.
  Encoding data from final_results.csv using the saved autoencoder model...
  
  === Checking Compatibility ===
  Checking compatibility of final_results.csv with the saved model...
  The model expects 97614 input features.
  Input file has 222 total columns.
  Found 8 metadata columns: ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']
  Found 214 potential feature columns.
  After excluding Orthogroup138309: 214 feature columns.
  ❌ The number of features (214) does NOT match what the model expects (97614).
     Missing 97400 features.
  Compatibility check failed. Please fix the issues before encoding.

Work dir:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/d59ed8554bfdcf940f77f5b3e19bd3

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
May-27 10:11:51.376 [TaskFinalizer-2] DEBUG nextflow.Session - Session aborted -- Cause: Process `AUTOENCODER (1)` terminated with an error exit status (1)
May-27 10:11:51.393 [TaskFinalizer-2] DEBUG nextflow.Session - The following nodes are still active:
[process] HMMSEARCH
  status=ACTIVE
  port 0: (value) OPEN  ; channel: __$eachinparam<0>
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (value) bound ; channel: __$eachinparam<2>
  port 3: (cntrl) -     ; channel: $

[process] PROCESS_HITS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (cntrl) -     ; channel: $

May-27 10:11:51.397 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 10:11:51.397 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 10:11:51.397 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 10:11:51.404 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (1)
May-27 10:11:51.418 [main] DEBUG n.executor.local.LocalTaskHandler - Unable to kill PRODIGAL (1) -- command: kill -TERM 204646; exit: 1 
 bash: line 0: kill: (204646) - No such process

May-27 10:11:51.422 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=1; failedCount=1; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=1; retriesCount=0; abortedCount=0; succeedDuration=39ms; failedDuration=7.3s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=3; peakMemory=4 GB; ]
May-27 10:11:52.322 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 10:11:52.489 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
